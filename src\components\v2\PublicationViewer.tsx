'use client';

import { useEffect, useState } from 'react';

import { BookData, PaperData, readBooksFromExcel, readPapersFromExcel } from '@/lib/excelUtils';

interface PublicationViewerProps {
  type: 'papers' | 'books';
  title: string;
}

export default function PublicationViewer({ type, title }: PublicationViewerProps) {
  const [papers, setPapers] = useState<PaperData[]>([]);
  const [books, setBooks] = useState<BookData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        if (type === 'papers') {
          const papersData = await readPapersFromExcel();
          setPapers(papersData);
        } else if (type === 'books') {
          const booksData = await readBooksFromExcel();
          setBooks(booksData);
        }
      } catch (error) {
        console.error(`Error loading ${type}:`, error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [type]);

  const handlePdfDownload = (id: number, type: 'papers' | 'books' = 'papers') => {
    const folder = type === 'papers' ? 'papers' : 'books';
    const pdfUrl = `/v2/${folder}/${id.toString().padStart(3, '0')}.pdf`;
    window.open(pdfUrl, '_blank');
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  const renderPapers = () => (
    <p style={{ textAlign: 'justify' }}>
      {[...papers].map((paper) => (
        <span key={paper.id}>
          <br />
          {paper.id}. <span className="font-times italic text-base">'{paper.title}'</span>
          <span className="font-times font-bold text-base">,</span>{' '}
          <span className="font-times font-bold text-base">{paper.authors}, </span>{' '}
          {paper.doi ? (
            <a
              href={paper.doi}
              className="hover:underline"
              style={{
                color: '#FAAC58',
                transition: 'color 0.2s ease',
              }}
              onMouseEnter={(e) => (e.currentTarget.style.color = '#FF7F00')}
              onMouseLeave={(e) => (e.currentTarget.style.color = '#FAAC58')}
            >
              <span className="font-times italic text-base">{paper.journal}</span>
              {paper.volume && paper.volume.trim() && (
                <span className="font-times font-bold text-base"> {paper.volume}, </span>
              )}{' '}
              {paper.pages && paper.pages.trim() && <span className="font-times text-base">{paper.pages}</span>}
              {((paper.volume && paper.volume.trim()) || (paper.pages && paper.pages.trim())) && (
                <span className="font-times font-bold text-base">,</span>
              )}{' '}
              <span className="font-times text-base">({paper.year})</span>
            </a>
          ) : (
            <>
              <span className="font-times italic text-base">{paper.journal}</span>
              {paper.volume && paper.volume.trim() && (
                <span className="font-times font-bold text-base"> {paper.volume}, </span>
              )}{' '}
              {paper.pages && paper.pages.trim() && <span className="font-times text-base">{paper.pages}</span>}
              {((paper.volume && paper.volume.trim()) || (paper.pages && paper.pages.trim())) && (
                <span className="font-times font-bold text-base">,</span>
              )}{' '}
              <span className="font-times text-base">({paper.year})</span>
            </>
          )}{' '}
          <button
            onClick={() => handlePdfDownload(paper.id, 'papers')}
            className="hover:underline cursor-pointer"
            style={{
              color: '#FAAC58',
              transition: 'color 0.2s ease',
            }}
            onMouseEnter={(e) => ((e.target as HTMLElement).style.color = '#FF7F00')}
            onMouseLeave={(e) => ((e.target as HTMLElement).style.color = '#FAAC58')}
          >
            (PDF)
          </button>
          <br />
        </span>
      ))}
    </p>
  );

  const renderBooks = () => (
    <p style={{ textAlign: 'justify' }}>
      {[...books].map((book) => (
        <span key={book.id}>
          <br />
          {book.id}. <span className="font-times italic text-base">'{book.title}'</span>
          <span className="font-times font-bold text-base">,</span>{' '}
          <span className="font-times font-bold text-base">{book.authors}, </span>{' '}
          <span className="font-times  text-base"> in </span>
          {book.doi ? (
            <a
              href={book.doi}
              className="hover:underline"
              style={{
                color: '#FAAC58',
                transition: 'color 0.2s ease',
              }}
              onMouseEnter={(e) => (e.currentTarget.style.color = '#FF7F00')}
              onMouseLeave={(e) => (e.currentTarget.style.color = '#FAAC58')}
            >
              <span className="font-times italic text-base">{book.book}</span>
              {book.pages && book.pages !== 'N/A' && book.pages.trim() && (
                <span className="font-times font-bold text-base">,</span>
              )}{' '}
              {book.volume && book.volume !== 'N/A' && book.volume.trim() && (
                <span className="font-times font-bold text-base"> {book.volume}, </span>
              )}{' '}
              {book.pages && book.pages.trim() && <span className="font-times text-base">{book.pages}</span>}
              {book.publisher && book.publisher.trim() && (
                <span className="font-times font-bold text-base">, {book.publisher}</span>
              )}
              <span className="font-times text-base">({book.year})</span>
            </a>
          ) : (
            <>
              <span className="font-times italic text-base">{book.book}</span>
              {book.pages && book.pages !== 'N/A' && book.pages.trim() && (
                <span className="font-times font-bold text-base">,</span>
              )}{' '}
              {book.volume && book.volume !== 'N/A' && book.volume.trim() && (
                <span className="font-times font-bold text-base"> {book.volume}, </span>
              )}{' '}
              {book.pages && book.pages.trim() && <span className="font-times text-base">{book.pages}</span>}
              {book.publisher && book.publisher.trim() && (
                <span className="font-times font-bold text-base">, {book.publisher}</span>
              )}
              <span className="font-times text-base">({book.year})</span>
            </>
          )}{' '}
          <button
            onClick={() => handlePdfDownload(book.id, 'books')}
            className="hover:underline cursor-pointer"
            style={{
              color: '#FAAC58',
              transition: 'color 0.2s ease',
            }}
            onMouseEnter={(e) => ((e.target as HTMLElement).style.color = '#FF7F00')}
            onMouseLeave={(e) => ((e.target as HTMLElement).style.color = '#FAAC58')}
          >
            (PDF)
          </button>
          <br />
        </span>
      ))}
    </p>
  );

  return (
    <section className={type === 'papers' ? 'mb-12' : ''}>
      <h2 className="text-2xl font-bold mb-4">{title}</h2>
      {type === 'papers' ? renderPapers() : renderBooks()}
    </section>
  );
}
